import re
from ewrc_complete import EWRCCompleteFetcher

# Test championship pattern matching
rally_championships = ["Greece", "Historic (GR)", "Rally3 (GR)", "Historic Gravel Cup (GR)"]

# Test text samples that should match
test_texts = [
    "Historic (GR)",
    "Historic Gravel Cup (GR)",
    "Historic (GR) Historic Gravel Cup (GR)",
    "Historic (GR)\nHistoric Gravel Cup (GR)",
    "Rally3 (GR)",
    "Greece",
]

print("Testing championship pattern matching:")
print("Rally championships:", rally_championships)
print()

for test_text in test_texts:
    print(f"Testing text: '{test_text}'")
    found_championships = []
    
    # Test the same logic as in the code
    for rally_championship in rally_championships:
        escaped_champ = re.escape(rally_championship)
        pattern = rf'\b{escaped_champ}\b'
        
        if re.search(pattern, test_text, re.IGNORECASE):
            if rally_championship not in found_championships:
                found_championships.append(rally_championship)
                print(f"  ✓ Found: {rally_championship}")
    
    if not found_championships:
        print("  ✗ No championships found")
    print()

# Test with actual EWRC fetcher
print("Testing with actual EWRC entries page...")
fetcher = EWRCCompleteFetcher(debug=False)

try:
    import requests
    entries_url = "https://www.ewrc-results.com/entries/94918-rally-stereas-elladas-2025/"
    response = requests.get(entries_url)
    
    # Look for entry #101 specifically
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # Find all table rows
    rows = soup.select('table tr')
    
    for i, row in enumerate(rows):
        row_text = row.get_text()
        if '#101' in row_text or 'Stafilopatis' in row_text:
            print(f"Found row {i} with #101:")
            print(f"Row text: '{row_text[:200]}...'")
            
            # Test championship detection on this row
            found_championships = []
            for rally_championship in rally_championships:
                escaped_champ = re.escape(rally_championship)
                pattern = rf'\b{escaped_champ}\b'
                
                if re.search(pattern, row_text, re.IGNORECASE):
                    if rally_championship not in found_championships:
                        found_championships.append(rally_championship)
            
            print(f"Championships found in this row: {found_championships}")
            break
    
except Exception as e:
    print(f"Error: {e}")
