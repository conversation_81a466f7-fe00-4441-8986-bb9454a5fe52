import json
from collections import defaultdict

# Load the JSON file
with open('ewrc_complete_results_94918-rally-stereas-elladas-2025.json', 'r') as f:
    data = json.load(f)

# Collect all unique championships
championships = set()
championship_counts = defaultdict(int)
entries_by_championship = defaultdict(list)

for entry in data['results']:
    entry_championships = entry.get('championship', [])
    championships.update(entry_championships)

    if entry_championships:
        for champ in entry_championships:
            championship_counts[champ] += 1
            entries_by_championship[champ].append(entry['entry_number'])
    else:
        championship_counts['NO_CHAMPIONSHIP'] += 1
        entries_by_championship['NO_CHAMPIONSHIP'].append(entry['entry_number'])

print('Unique championships found:')
for c in sorted(championships):
    print(f'  "{c}" - {championship_counts[c]} entries')

print(f'\nTotal unique championships: {len(championships)}')

# Count entries with championships vs without
with_championships = sum(1 for entry in data['results'] if entry.get('championship'))
without_championships = sum(1 for entry in data['results'] if not entry.get('championship'))

print(f'Entries with championships: {with_championships}')
print(f'Entries without championships: {without_championships}')

# Show some examples of entries without championships
print(f'\nSample entries without championships:')
no_champ_entries = [entry for entry in data['results'] if not entry.get('championship')][:10]
for entry in no_champ_entries:
    print(f'  {entry["entry_number"]}: {entry["driver_codriver"]} - {entry["car"]} (Group: {entry["group"]})')

# Show Rally3 cars that might be missing Rally3 championship
print(f'\nRally3 cars (should have Rally3 championship):')
rally3_entries = [entry for entry in data['results'] if 'rally3' in entry['car'].lower()][:10]
for entry in rally3_entries:
    champs = entry.get('championship', [])
    print(f'  {entry["entry_number"]}: {entry["car"]} - Championships: {champs}')
