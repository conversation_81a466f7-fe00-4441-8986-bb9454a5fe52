import json
from collections import defaultdict

# Load the JSON file
with open('ewrc_complete_results_94918-rally-stereas-elladas-2025.json', 'r') as f:
    data = json.load(f)

# Collect all unique championships
championships = set()
championship_counts = defaultdict(int)

for entry in data['results']:
    entry_championships = entry.get('championship', [])
    championships.update(entry_championships)
    
    if entry_championships:
        for champ in entry_championships:
            championship_counts[champ] += 1
    else:
        championship_counts['NO_CHAMPIONSHIP'] += 1

print('Championship extraction results:')
for c in sorted(championships):
    print(f'  "{c}" - {championship_counts[c]} entries')

print(f'\nTotal unique championships: {len(championships)}')

# Count entries with championships vs without
with_championships = sum(1 for entry in data['results'] if entry.get('championship'))
without_championships = sum(1 for entry in data['results'] if not entry.get('championship'))

print(f'Entries with championships: {with_championships}')
print(f'Entries without championships: {without_championships}')
print(f'Success rate: {with_championships/(with_championships+without_championships)*100:.1f}%')
